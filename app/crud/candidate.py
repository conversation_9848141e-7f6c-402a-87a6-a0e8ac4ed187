"""
候选人CRUD操作
"""
from typing import List, Optional
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.candidate import Candidate
from app.models.enums import PlatformEnum, FollowUpStatusEnum
from app.schemas.candidate import CandidateCreate, CandidateUpdate


class CRUDCandidate(CRUDBase[Candidate, CandidateCreate, CandidateUpdate]):
    """候选人CRUD操作类"""
    
    def get_by_kol_id(self, db: Session, *, kol_id: Optional[int]) -> Optional[Candidate]:
        """根据KOL ID获取候选人 - 使用idx_candidates_kol_id索引"""
        if kol_id is None:
            return None
        return db.query(Candidate).filter(Candidate.kol_id == kol_id).first()

    def get_by_kol_id_and_project(self, db: Session, *, kol_id: Optional[int], project_code: str) -> Optional[Candidate]:
        """根据KOL ID和项目代码获取候选人"""
        if kol_id is None:
            return None
        return db.query(Candidate).filter(
            Candidate.kol_id == kol_id,
            Candidate.project_code == project_code
        ).first()
    
    def get_by_project(
        self,
        db: Session,
        *,
        project_code: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """根据项目代码获取候选人列表 - 使用idx_candidates_project_code索引"""
        return (
            db.query(Candidate)
            .filter(Candidate.project_code == project_code)
            .order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_status(
        self,
        db: Session,
        *,
        status: FollowUpStatusEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """根据跟进状态获取候选人 - 优化使用idx_candidates_project_status复合索引"""
        if project_code:
            # 使用复合索引 idx_candidates_project_status (project_code, follow_up_status)
            query = db.query(Candidate).filter(
                Candidate.project_code == project_code,
                Candidate.follow_up_status == status
            )
        else:
            # 使用单列索引 idx_candidates_follow_up_status
            query = db.query(Candidate).filter(Candidate.follow_up_status == status)
        
        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_tracker(
        self,
        db: Session,
        *,
        tracker: str,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """根据跟进人获取候选人"""
        query = db.query(Candidate).filter(Candidate.tracker == tracker)
        
        if project_code:
            query = query.filter(Candidate.project_code == project_code)
        
        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_platform(
        self,
        db: Session,
        *,
        platform: PlatformEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """根据平台获取候选人"""
        query = db.query(Candidate).filter(Candidate.platform == platform)
        
        if project_code:
            query = query.filter(Candidate.project_code == project_code)
        
        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_thread_id(self, db: Session, *, thread_id: str) -> Optional[Candidate]:
        """根据邮件线程ID获取候选人"""
        return db.query(Candidate).filter(Candidate.thread_id == thread_id).first()

    def get_by_need_review(
        self,
        db: Session,
        *,
        need_review: bool,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """根据need_review状态获取候选人 - 使用idx_candidates_need_review或idx_candidates_project_need_review索引"""
        if project_code:
            # 使用复合索引 idx_candidates_project_need_review (project_code, need_review)
            query = db.query(Candidate).filter(
                Candidate.project_code == project_code,
                Candidate.need_review == need_review
            )
        else:
            # 使用单列索引 idx_candidates_need_review
            query = db.query(Candidate).filter(Candidate.need_review == need_review)

        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_candidates_without_kol(
        self,
        db: Session,
        *,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """获取没有关联KOL的候选人"""
        query = db.query(Candidate).filter(Candidate.kol_id.is_(None))

        if project_code:
            query = query.filter(Candidate.project_code == project_code)

        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def update_follow_up_status(
        self,
        db: Session,
        *,
        candidate_id: int,
        status: FollowUpStatusEnum,
        note: Optional[str] = None
    ) -> Optional[Candidate]:
        """更新跟进状态"""
        db_obj = self.get(db, id=candidate_id)
        if not db_obj:
            return None
        
        update_data = {"follow_up_status": status}
        if note:
            update_data["follow_up_note"] = note
        
        return self.update(db, db_obj=db_obj, obj_in=update_data)
    
    def assign_tracker(
        self,
        db: Session,
        *,
        candidate_id: int,
        tracker: str
    ) -> Optional[Candidate]:
        """分配跟进人"""
        db_obj = self.get(db, id=candidate_id)
        if not db_obj:
            return None
        
        return self.update(db, db_obj=db_obj, obj_in={"tracker": tracker})
    
    def search_candidates(
        self,
        db: Session,
        *,
        nick_name: Optional[str] = None,
        social_id: Optional[str] = None,
        platform: Optional[PlatformEnum] = None,
        status: Optional[FollowUpStatusEnum] = None,
        tracker: Optional[str] = None,
        project_code: Optional[str] = None,
        reply_email: Optional[str] = None,
        need_review: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Candidate]:
        """搜索候选人"""
        query = db.query(Candidate)
        
        if nick_name:
            query = query.filter(Candidate.nick_name.ilike(f"%{nick_name}%"))

        if social_id:
            query = query.filter(Candidate.social_id.ilike(f"%{social_id}%"))

        if platform:
            query = query.filter(Candidate.platform == platform)
        
        if status:
            query = query.filter(Candidate.follow_up_status == status)
        
        if tracker:
            query = query.filter(Candidate.tracker.ilike(f"%{tracker}%"))
        
        if project_code:
            query = query.filter(Candidate.project_code == project_code)
        
        if reply_email:
            query = query.filter(Candidate.reply_email_addr.ilike(f"%{reply_email}%"))

        if need_review is not None:
            query = query.filter(Candidate.need_review == need_review)

        return (
            query.order_by(Candidate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_candidate_statistics(self, db: Session, *, project_code: Optional[str] = None) -> dict:
        """获取候选人统计信息"""
        query = db.query(Candidate)
        
        if project_code:
            query = query.filter(Candidate.project_code == project_code)
        
        total = query.count()
        
        # 按状态统计
        status_stats = {}
        for status in FollowUpStatusEnum:
            count = query.filter(Candidate.follow_up_status == status).count()
            status_stats[status.value] = count
        
        # 按平台统计
        platform_stats = {}
        for platform in PlatformEnum:
            count = query.filter(Candidate.platform == platform).count()
            platform_stats[platform.value] = count
        
        # 按跟进人统计
        tracker_stats = {}
        trackers = db.query(Candidate.tracker).filter(
            Candidate.tracker.isnot(None)
        ).distinct().all()
        
        for (tracker_name,) in trackers:
            count = query.filter(Candidate.tracker == tracker_name).count()
            tracker_stats[tracker_name] = count
        
        return {
            "total": total,
            "status_stats": status_stats,
            "platform_stats": platform_stats,
            "tracker_stats": tracker_stats
        }
    
    def create_candidate(self, db: Session, *, obj_in: CandidateCreate) -> Candidate:
        """创建候选人，检查是否已存在，并校验KOL关联"""
        # 如果传入了social_id, platform, project_code三个字段，需要校验KOL是否存在
        if obj_in.social_id and obj_in.platform and obj_in.project_code:
            from app.crud.kol import kol as kol_crud

            # 根据三个字段查找KOL
            kol = kol_crud.get_by_social_id_platform_project(
                db,
                social_id=obj_in.social_id,
                platform=obj_in.platform,
                project_code=obj_in.project_code
            )

            if not kol:
                raise ValueError(
                    f"在KOL主表中找不到匹配的记录: social_id={obj_in.social_id}, "
                    f"platform={obj_in.platform.value}, project_code={obj_in.project_code}"
                )

            # 将找到的KOL主键ID回填到candidates的kol_id字段（转换为字符串）
            obj_in.kol_id = str(kol.id)

        # 如果提供了kol_id，检查该KOL是否已经是候选人
        if obj_in.kol_id is not None:
            # 由于kol_id在candidate表中是字符串类型，需要特殊处理查询
            existing = db.query(Candidate).filter(Candidate.kol_id == obj_in.kol_id).first()
            if existing:
                raise ValueError(f"KOL ID '{obj_in.kol_id}' 已经是候选人")

        return self.create(db, obj_in=obj_in)

    def update_candidate(self, db: Session, *, db_obj: Candidate, obj_in: CandidateUpdate) -> Candidate:
        """更新候选人，校验KOL关联"""
        # 组合传入参数和数据库中已有的字段来获取完整的三个字段
        final_social_id = obj_in.social_id if obj_in.social_id is not None else db_obj.social_id
        final_platform = obj_in.platform if obj_in.platform is not None else db_obj.platform
        final_project_code = obj_in.project_code if obj_in.project_code is not None else db_obj.project_code

        # 如果能够组合成完整的三个字段，则需要校验KOL是否存在
        if final_social_id and final_platform and final_project_code:
            from app.crud.kol import kol as kol_crud

            # 根据组合后的三个字段查找KOL
            kol = kol_crud.get_by_social_id_platform_project(
                db,
                social_id=final_social_id,
                platform=final_platform,
                project_code=final_project_code
            )

            if not kol:
                raise ValueError(
                    f"在KOL主表中找不到匹配的记录: social_id={final_social_id}, "
                    f"platform={final_platform.value}, project_code={final_project_code}"
                )

            # 将找到的KOL主键ID回填到candidates的kol_id字段（转换为字符串）
            obj_in.kol_id = str(kol.id)

        # 如果提供了kol_id，检查该KOL是否已经是其他候选人
        if obj_in.kol_id is not None:
            existing = db.query(Candidate).filter(
                Candidate.kol_id == obj_in.kol_id,
                Candidate.id != db_obj.id  # 排除当前候选人
            ).first()
            if existing:
                raise ValueError(f"KOL ID '{obj_in.kol_id}' 已经是其他候选人")

        return self.update(db, db_obj=db_obj, obj_in=obj_in)

    def link_candidate_to_kol(
        self,
        db: Session,
        *,
        candidate_id: int,
        kol_id: int,
        update_platform: bool = True
    ) -> Optional[Candidate]:
        """将候选人关联到KOL，并可选择性地同步平台信息"""
        from app.crud.kol import kol as kol_crud

        # 检查候选人是否存在
        db_candidate = self.get(db, id=candidate_id)
        if not db_candidate:
            return None

        # 检查KOL是否存在
        db_kol = kol_crud.get(db, id=kol_id)
        if not db_kol:
            raise ValueError(f"KOL ID '{kol_id}' 不存在")

        # 检查该KOL是否已经关联到其他候选人
        existing = self.get_by_kol_id(db, kol_id=kol_id)
        if existing and existing.id != candidate_id:
            raise ValueError(f"KOL '{kol_id}' 已经关联到其他候选人")

        # 更新候选人信息
        update_data = {"kol_id": str(kol_id)}
        if update_platform:
            update_data.update({
                "platform": db_kol.platform,
                "social_id": db_kol.social_id,
                "nick_name": db_kol.nick_name
            })

        return self.update(db, db_obj=db_candidate, obj_in=update_data)


# 创建CRUD实例
candidate = CRUDCandidate(Candidate)
